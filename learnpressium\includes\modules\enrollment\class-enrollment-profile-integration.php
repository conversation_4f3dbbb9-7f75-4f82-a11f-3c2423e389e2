<?php
/**
 * LearnPress Profile Integration for Enrollment Module
 * 
 * Adds "Scheduled Course" tab to LearnPress user profile
 * 
 * @package Learnpressium
 * @subpackage Enrollment
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class Enrollment_Profile_Integration
 * 
 * Integrates scheduled enrollment functionality with LearnPress user profiles
 */
class Enrollment_Profile_Integration {
    
    /**
     * @var Enrollment_Manager
     */
    private $enrollment_manager;
    
    /**
     * Constructor
     * 
     * @param Enrollment_Manager $enrollment_manager
     */
    public function __construct($enrollment_manager) {
        $this->enrollment_manager = $enrollment_manager;
    }
    
    /**
     * Initialize profile integration
     */
    public function init() {
        // Add "Scheduled Course" tab to LearnPress profile course tabs
        add_filter('lp/profile/user_courses_attend/subtask', array($this, 'add_scheduled_course_tab'));

        // Intercept LearnPress REST API for scheduled courses
        add_action('learnpress/rest/frontend/profile/course_tab', array($this, 'handle_scheduled_courses_rest_api'));

        // Add scheduled courses to statistics
        add_filter('lp/profile/student/statistic', array($this, 'add_scheduled_courses_to_statistics'), 10, 2);

        // Debug: Log that profile integration is initialized
        error_log('Learnpressium: Profile integration initialized');
    }
    
    /**
     * Add "Scheduled Course" tab to LearnPress profile course tabs
     * 
     * @param array $tabs Existing course tabs
     * @return array Modified tabs with scheduled course tab
     */
    public function add_scheduled_course_tab($tabs) {
        // Add our "Scheduled Course" tab
        $tabs['scheduled'] = __('Scheduled Course', 'learnpressium');
        
        // Debug: Log that tab is being added
        error_log('Learnpressium: Adding Scheduled Course tab to profile');
        error_log('Profile tabs: ' . print_r($tabs, true));
        
        return $tabs;
    }
    
    /**
     * Handle REST API request for scheduled courses content
     * This intercepts LearnPress's course tab REST API when status is 'scheduled'
     */
    public function handle_scheduled_courses_rest_api($params) {
        // Check if this is a request for scheduled courses
        $status = isset($params['status']) ? sanitize_text_field($params['status']) : '';

        if ($status !== 'scheduled') {
            return; // Not our request, let LearnPress handle it
        }

        // Debug: Log that we're handling scheduled courses REST API
        error_log('Learnpressium: Handling scheduled courses REST API request');
        error_log('Request params: ' . print_r($params, true));

        // Get user ID
        $user_id = isset($params['userID']) ? intval($params['userID']) : 0;

        if (!$user_id) {
            // This will be handled by LearnPress's error handling
            return;
        }

        // Get scheduled courses for this user
        $scheduled_courses = $this->get_user_scheduled_courses($user_id);

        // Generate content and output it directly
        echo $this->generate_scheduled_courses_content($scheduled_courses, $user_id);

        // Stop further processing by LearnPress
        exit;
    }
    
    /**
     * Add scheduled courses count to user statistics
     * 
     * @param array $statistic Current statistics
     * @param LP_User $user User object
     * @return array Modified statistics
     */
    public function add_scheduled_courses_to_statistics($statistic, $user) {
        if (!$user) {
            return $statistic;
        }
        
        // Get scheduled courses count for this user
        $scheduled_count = $this->get_user_scheduled_courses_count($user->get_id());
        
        // Add to statistics
        $statistic['scheduled_courses'] = $scheduled_count;
        
        // Debug: Log statistics update
        error_log("Learnpressium: Added {$scheduled_count} scheduled courses to user {$user->get_id()} statistics");
        
        return $statistic;
    }
    
    /**
     * Get count of scheduled courses for a user
     * 
     * @param int $user_id User ID
     * @return int Number of scheduled courses
     */
    private function get_user_scheduled_courses_count($user_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'learnpressium_enrollment_schedules';
        
        $count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$table_name} WHERE user_id = %d AND status = 'scheduled'",
            $user_id
        ));
        
        return intval($count);
    }
    
    /**
     * Get scheduled courses for a user
     * 
     * @param int $user_id User ID
     * @param array $args Query arguments
     * @return array Scheduled courses data
     */
    public function get_user_scheduled_courses($user_id, $args = array()) {
        global $wpdb;
        
        $defaults = array(
            'limit' => 10,
            'offset' => 0,
            'status' => 'scheduled'
        );
        
        $args = wp_parse_args($args, $defaults);
        
        $table_name = $wpdb->prefix . 'learnpressium_enrollment_schedules';
        
        $query = $wpdb->prepare(
            "SELECT * FROM {$table_name} 
             WHERE user_id = %d AND status = %s 
             ORDER BY scheduled_start_date ASC 
             LIMIT %d OFFSET %d",
            $user_id,
            $args['status'],
            $args['limit'],
            $args['offset']
        );
        
        $results = $wpdb->get_results($query);
        
        return $results ? $results : array();
    }

    /**
     * Generate HTML content for scheduled courses
     *
     * @param array $scheduled_courses Array of scheduled course data
     * @param int $user_id User ID
     * @return string HTML content
     */
    private function generate_scheduled_courses_content($scheduled_courses, $user_id) {
        if (empty($scheduled_courses)) {
            return '<div class="learn-press-course-tab__filter__content" data-tab="scheduled">' .
                   '<p style="text-align: center; padding: 40px; font-size: 16px; color: #666;">' .
                   __('No scheduled courses found.', 'learnpressium') .
                   '</p></div>';
        }

        $content = '<div class="learn-press-course-tab__filter__content" data-tab="scheduled">';
        $content .= '<table class="lp_profile_course_progress lp-list-table">';
        $content .= '<thead>';
        $content .= '<tr class="lp_profile_course_progress__item lp_profile_course_progress__header">';
        $content .= '<th></th>';
        $content .= '<th>' . __('Course Name', 'learnpressium') . '</th>';
        $content .= '<th>' . __('Scheduled Start', 'learnpressium') . '</th>';
        $content .= '<th>' . __('Status', 'learnpressium') . '</th>';
        $content .= '<th>' . __('Notes', 'learnpressium') . '</th>';
        $content .= '</tr>';
        $content .= '</thead>';
        $content .= '<tbody>';

        foreach ($scheduled_courses as $schedule) {
            $course = get_post($schedule->course_id);
            if (!$course) {
                continue;
            }

            $course_url = get_permalink($course->ID);
            $course_title = get_the_title($course->ID);
            $course_thumbnail = get_the_post_thumbnail($course->ID, 'thumbnail');

            // Format dates
            $start_date = date('M j, Y g:i A', strtotime($schedule->scheduled_start_date));
            $current_time = current_time('mysql');

            // Determine status
            if ($schedule->status === 'scheduled') {
                if ($schedule->scheduled_start_date <= $current_time) {
                    $status_label = '<span style="color: #f39c12;">' . __('Ready to Start', 'learnpressium') . '</span>';
                } else {
                    $status_label = '<span style="color: #3498db;">' . __('Scheduled', 'learnpressium') . '</span>';
                }
            } else {
                $status_label = '<span style="color: #27ae60;">' . ucfirst($schedule->status) . '</span>';
            }

            $content .= '<tr class="lp_profile_course_progress__item">';
            $content .= '<td>';
            if ($course_thumbnail) {
                $content .= '<a href="' . esc_url($course_url) . '">' . $course_thumbnail . '</a>';
            } else {
                $content .= '<div style="width: 60px; height: 60px; background: #f0f0f0; display: flex; align-items: center; justify-content: center; border-radius: 4px;">';
                $content .= '<span style="color: #999; font-size: 12px;">No Image</span>';
                $content .= '</div>';
            }
            $content .= '</td>';
            $content .= '<td><a href="' . esc_url($course_url) . '">' . esc_html($course_title) . '</a></td>';
            $content .= '<td>' . esc_html($start_date) . '</td>';
            $content .= '<td>' . $status_label . '</td>';
            $content .= '<td>' . esc_html($schedule->notes ?: '-') . '</td>';
            $content .= '</tr>';
        }

        $content .= '</tbody>';
        $content .= '</table>';
        $content .= '</div>';

        return $content;
    }
}
