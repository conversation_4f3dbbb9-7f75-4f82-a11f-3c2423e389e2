{"name": "johnbillion/user-switching", "description": "Instant switching between user accounts in WordPress.", "license": "GPL-2.0-or-later", "type": "wordpress-plugin", "authors": [{"name": "<PERSON>", "homepage": "https://johnblackbourn.com/"}], "homepage": "https://github.com/johnbillion/user-switching/", "support": {"issues": "https://github.com/johnbillion/user-switching/issues", "forum": "https://wordpress.org/support/plugin/user-switching", "source": "https://github.com/johnbillion/user-switching", "security": "https://patchstack.com/database/vdp/user-switching"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/johnbillion"}], "require": {"php": ">=7.4", "composer/installers": "^1.0 || ^2.0"}, "require-dev": {"codeception/module-asserts": "^1.0", "codeception/module-db": "^1.0", "codeception/module-webdriver": "^1.0", "codeception/util-universalframework": "^1.0", "johnbillion/plugin-infrastructure": "dev-trunk", "johnbillion/wp-compat": "0.3.0", "lucatume/wp-browser": "3.2.3", "phpcompatibility/phpcompatibility-wp": "2.1.5", "phpstan/phpstan": "1.12.11", "phpstan/phpstan-phpunit": "1.4.1", "roots/wordpress-core-installer": "1.100.0", "roots/wordpress-full": "*", "swissspidy/phpstan-no-private": "^0.2.1", "szepeviktor/phpstan-wordpress": "1.3.5", "wp-coding-standards/wpcs": "3.1.0"}, "autoload-dev": {"psr-4": {"UserSwitching\\Tests\\": "tests/integration"}}, "config": {"allow-plugins": {"composer/installers": true, "roots/wordpress-core-installer": true, "dealerdirect/phpcodesniffer-composer-installer": true}, "classmap-authoritative": true, "preferred-install": "dist", "prepend-autoloader": false, "sort-packages": true}, "extra": {"wordpress-install-dir": "vendor/wordpress/wordpress"}, "scripts": {"test": ["@composer validate --strict --no-check-lock", "@test:phpstan", "@test:phpcs", "@test:start", "@test:integration", "@test:acceptance", "@test:stop"], "test:acceptance": ["acceptance-tests --cli=\"language core install it_IT\" --cli=\"language plugin install user-switching it_IT\""], "test:destroy": ["tests-destroy"], "test:integration": ["integration-tests"], "test:phpcs": ["phpcs -ps --colors --report-code --report-summary --report-width=80 --basepath='./' ."], "test:phpstan": ["codecept build", "phpstan analyze -v --memory-limit=1024M"], "test:start": ["tests-start"], "test:stop": ["tests-stop"]}}