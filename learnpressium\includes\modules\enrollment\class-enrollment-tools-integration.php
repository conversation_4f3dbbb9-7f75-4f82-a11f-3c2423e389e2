<?php

/**
 * The Enrollment Tools Integration class
 *
 * Integrates enrollment scheduling with LearnPress Tools
 *
 * @package    Learnpressium
 * @subpackage Learnpressium/includes/modules/enrollment
 */

class Enrollment_Tools_Integration {

    /**
     * The enrollment manager instance
     */
    private $enrollment_manager;

    /**
     * Constructor
     */
    public function __construct($enrollment_manager) {
        $this->enrollment_manager = $enrollment_manager;
    }

    /**
     * Initialize tools integration
     */
    public function init() {
        // REMOVED: Separate tab approach - integrating directly into existing Assign Course instead
        // add_filter('learn-press/admin/tools-tabs', array($this, 'add_tools_tab'));

        // Enhance existing assign/unassign forms with scheduling options
        add_action('admin_footer', array($this, 'enhance_assignment_forms'));
        
        // Handle scheduled assignments via AJAX
        add_action('wp_ajax_learnpressium_tools_schedule_assignment', array($this, 'handle_scheduled_assignment'));
        
        // Add REST API endpoints for tools integration
        add_action('rest_api_init', array($this, 'register_rest_routes'));
    }

    /**
     * Add scheduled enrollment tab to LearnPress Tools
     */
    public function add_tools_tab($tabs) {
        $tabs['scheduled_enrollment'] = __('Scheduled Enrollment', 'learnpressium');

        // Debug: Log that our tab is being added
        error_log('Learnpressium: Adding Scheduled Enrollment tab to LearnPress Tools');

        return $tabs;
    }

    /**
     * Temporary debug notice to verify module is working
     */
    public function debug_notice() {
        $screen = get_current_screen();
        if ($screen && $screen->id === 'learnpress_page_learn-press-tools') {
            echo '<div class="notice notice-info"><p><strong>Learnpressium Enrollment Module:</strong> Tools integration is active. Look for the "Scheduled Enrollment" tab above.</p></div>';
        }
    }

    /**
     * Display content for scheduled enrollment tools tab
     */
    public function display_tools_content() {
        // Debug: Log that content is being displayed
        error_log('Learnpressium: Displaying Scheduled Enrollment content');
        ?>
        <div id="learnpressium-scheduled-enrollment-tools">
            <div class="card">
                <h2><?php _e('Schedule Course Enrollment', 'learnpressium'); ?></h2>
                <div class="description">
                    <p><?php _e('Schedule users to access courses at specific dates and times.', 'learnpressium'); ?></p>
                    <p><strong><?php _e('Note:', 'learnpressium'); ?></strong> <?php _e('Users will be enrolled immediately but cannot access the course content until the scheduled start date.', 'learnpressium'); ?></p>
                </div>
                
                <div class="content">
                    <form id="lp-schedule-enrollment-form">
                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="schedule-course-select"><?php _e('Select Course', 'learnpressium'); ?></label>
                                </th>
                                <td>
                                    <select id="schedule-course-select" name="course_ids[]" multiple required>
                                        <!-- Populated via AJAX -->
                                    </select>
                                    <p class="description"><?php _e('Select one or more courses', 'learnpressium'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="schedule-user-select"><?php _e('Select Users', 'learnpressium'); ?></label>
                                </th>
                                <td>
                                    <select id="schedule-user-select" name="user_ids[]" multiple required>
                                        <!-- Populated via AJAX -->
                                    </select>
                                    <p class="description"><?php _e('Select one or more users', 'learnpressium'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="schedule-start-date"><?php _e('Start Date & Time', 'learnpressium'); ?></label>
                                </th>
                                <td>
                                    <input type="text" id="schedule-start-date" name="start_date" class="datepicker" required>
                                    <input type="time" id="schedule-start-time" name="start_time" value="09:00" required>
                                    <p class="description"><?php _e('When users can start accessing the course', 'learnpressium'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="schedule-end-date"><?php _e('End Date & Time (Optional)', 'learnpressium'); ?></label>
                                </th>
                                <td>
                                    <input type="text" id="schedule-end-date" name="end_date" class="datepicker">
                                    <input type="time" id="schedule-end-time" name="end_time" value="23:59">
                                    <p class="description"><?php _e('When access expires (leave empty for no expiration)', 'learnpressium'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="schedule-notes"><?php _e('Notes', 'learnpressium'); ?></label>
                                </th>
                                <td>
                                    <textarea id="schedule-notes" name="notes" rows="3" cols="50"></textarea>
                                    <p class="description"><?php _e('Optional notes about this scheduled enrollment', 'learnpressium'); ?></p>
                                </td>
                            </tr>
                        </table>
                        
                        <div class="submit-section">
                            <button type="submit" class="button button-primary lp-button-schedule-enrollment">
                                <?php _e('Schedule Enrollment', 'learnpressium'); ?>
                            </button>
                            <span class="percent" style="margin-left: 10px"></span>
                            <span class="message" style="margin-left: 10px"></span>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Bulk Schedule Management -->
            <div class="card" style="margin-top: 20px;">
                <h2><?php _e('Manage Scheduled Enrollments', 'learnpressium'); ?></h2>
                <div class="description">
                    <p><?php _e('View and manage existing scheduled enrollments.', 'learnpressium'); ?></p>
                </div>
                
                <div class="content">
                    <div class="tablenav top">
                        <div class="alignleft actions">
                            <select id="bulk-status-filter">
                                <option value=""><?php _e('All Statuses', 'learnpressium'); ?></option>
                                <option value="scheduled"><?php _e('Scheduled', 'learnpressium'); ?></option>
                                <option value="active"><?php _e('Active', 'learnpressium'); ?></option>
                                <option value="expired"><?php _e('Expired', 'learnpressium'); ?></option>
                            </select>
                            <button type="button" class="button" id="filter-bulk-schedules"><?php _e('Filter', 'learnpressium'); ?></button>
                        </div>
                        <div class="alignright actions">
                            <select id="bulk-actions">
                                <option value=""><?php _e('Bulk Actions', 'learnpressium'); ?></option>
                                <option value="activate"><?php _e('Activate Now', 'learnpressium'); ?></option>
                                <option value="expire"><?php _e('Expire Now', 'learnpressium'); ?></option>
                                <option value="delete"><?php _e('Delete', 'learnpressium'); ?></option>
                            </select>
                            <button type="button" class="button" id="apply-bulk-action"><?php _e('Apply', 'learnpressium'); ?></button>
                        </div>
                    </div>

                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <td class="manage-column column-cb check-column">
                                    <input type="checkbox" id="select-all-schedules">
                                </td>
                                <th><?php _e('User', 'learnpressium'); ?></th>
                                <th><?php _e('Course', 'learnpressium'); ?></th>
                                <th><?php _e('Start Date', 'learnpressium'); ?></th>
                                <th><?php _e('End Date', 'learnpressium'); ?></th>
                                <th><?php _e('Status', 'learnpressium'); ?></th>
                                <th><?php _e('Actions', 'learnpressium'); ?></th>
                            </tr>
                        </thead>
                        <tbody id="bulk-schedules-tbody">
                            <tr>
                                <td colspan="7" class="text-center">
                                    <span class="spinner is-active"></span>
                                    <?php _e('Loading schedules...', 'learnpressium'); ?>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <style>
        #learnpressium-scheduled-enrollment-tools .form-table th {
            width: 200px;
        }

        #learnpressium-scheduled-enrollment-tools .datepicker {
            width: 150px;
        }

        #learnpressium-scheduled-enrollment-tools input[type="time"] {
            width: 100px;
            margin-left: 10px;
        }

        #learnpressium-scheduled-enrollment-tools select[multiple] {
            height: 120px;
            width: 100%;
        }

        #learnpressium-scheduled-enrollment-tools .submit-section {
            padding: 20px 0;
            border-top: 1px solid #ddd;
            margin-top: 20px;
        }

        .status-scheduled {
            color: #0073aa;
            font-weight: bold;
        }

        .status-active {
            color: #46b450;
            font-weight: bold;
        }

        .status-expired {
            color: #dc3232;
            font-weight: bold;
        }

        .text-center {
            text-align: center;
        }

        .check-column {
            width: 2.2em;
        }
        </style>
        <?php
    }

    /**
     * Enhance existing assignment forms with scheduling options
     */
    public function enhance_assignment_forms() {
        $screen = get_current_screen();

        // Only add to LearnPress Tools page
        if (!$screen || $screen->id !== 'learnpress_page_learn-press-tools') {
            return;
        }
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Wait for the page to be fully loaded
            setTimeout(function() {
                // Check if the assign course form exists
                if ($('#lp-assign-user-course-form').length && $('#lp-assign-user-course-form ul').length) {

                    // Create scheduling options HTML
                    var schedulingHtml = '<li style="border-top: 1px solid #ddd; padding-top: 15px; margin-top: 15px;">' +
                        '<h4>📅 Scheduling Options (Learnpressium)</h4>' +
                        '<label style="display: block; margin-bottom: 10px;">' +
                            '<input type="checkbox" id="enable-scheduling" name="enable_scheduling" value="1" style="margin-right: 8px;">' +
                            'Schedule this enrollment for a future date' +
                        '</label>' +
                        '<div id="scheduling-options" style="display: none; margin-top: 10px; padding: 15px; background: #f9f9f9; border: 1px solid #ddd; border-radius: 4px;">' +
                            '<div style="margin-bottom: 15px;">' +
                                '<label style="display: block; font-weight: 600; margin-bottom: 5px;">Start Date & Time:</label>' +
                                '<input type="date" id="assign-start-date" name="schedule_start_date" style="width: 150px; margin-right: 10px;">' +
                                '<input type="time" id="assign-start-time" name="schedule_start_time" value="09:00" style="width: 100px;">' +
                            '</div>' +
                            '<div style="margin-bottom: 15px;">' +
                                '<label style="display: block; font-weight: 600; margin-bottom: 5px;">End Date & Time (Optional):</label>' +
                                '<input type="date" id="assign-end-date" name="schedule_end_date" style="width: 150px; margin-right: 10px;">' +
                                '<input type="time" id="assign-end-time" name="schedule_end_time" value="23:59" style="width: 100px;">' +
                            '</div>' +
                            '<div>' +
                                '<label style="display: block; font-weight: 600; margin-bottom: 5px;">Notes:</label>' +
                                '<textarea id="assign-notes" name="schedule_notes" rows="2" style="width: 100%; resize: vertical;" placeholder="Optional notes about this scheduled enrollment..."></textarea>' +
                            '</div>' +
                        '</div>' +
                    '</li>';

                    // Append to the form
                    $('#lp-assign-user-course-form ul').append(schedulingHtml);

                    // Toggle scheduling options
                    $('#enable-scheduling').change(function() {
                        $('#scheduling-options').toggle(this.checked);
                    });

                    // Completely intercept form submission when scheduling is enabled
                    $('#lp-assign-user-course-form').on('submit', function(e) {
                        if ($('#enable-scheduling').is(':checked')) {
                            e.preventDefault();
                            e.stopPropagation();
                            e.stopImmediatePropagation();
                            handleScheduledAssignment();
                            return false;
                        }
                    });

                    // Also intercept any click events on the assign button when scheduling is enabled
                    $(document).on('click', '#lp-assign-user-course-form .lp-button-assign, #lp-assign-user-course-form .lp-button-assign-course', function(e) {
                        if ($('#enable-scheduling').is(':checked')) {
                            e.preventDefault();
                            e.stopPropagation();
                            e.stopImmediatePropagation();
                            handleScheduledAssignment();
                            return false;
                        }
                    });

                    console.log('Learnpressium: Scheduling options added to Assign Course form');
                } else {
                    console.log('Learnpressium: Assign Course form not found');
                }
            }, 1000); // Wait 1 second for LearnPress to load its form

            // Function to handle scheduled assignment
            function handleScheduledAssignment() {
                // Get the correct field values - LearnPress uses different field names
                var courseIds = [];
                var userIds = [];

                // Try different possible field names
                var courseField = $('#lp-assign-user-course-form select[name="course_ids"]').val() ||
                                 $('#lp-assign-user-course-form select[name="course-id"]').val() ||
                                 $('#lp-assign-user-course-form select[name="course_id"]').val();

                var userField = $('#lp-assign-user-course-form select[name="user_ids"]').val() ||
                               $('#lp-assign-user-course-form select[name="user-id"]').val() ||
                               $('#lp-assign-user-course-form select[name="user_id"]').val();

                if (Array.isArray(courseField)) {
                    courseIds = courseField;
                } else if (courseField) {
                    courseIds = [courseField];
                }

                if (Array.isArray(userField)) {
                    userIds = userField;
                } else if (userField) {
                    userIds = [userField];
                }

                console.log('Course IDs:', courseIds);
                console.log('User IDs:', userIds);

                if (courseIds.length === 0 || userIds.length === 0) {
                    alert('❌ Please select both course and user before scheduling.');
                    return;
                }

                var formData = {
                    action: 'learnpressium_tools_schedule_assignment',
                    nonce: '<?php echo wp_create_nonce('learnpressium_enrollment_nonce'); ?>',
                    course_ids: courseIds,
                    user_ids: userIds,
                    start_date: $('#assign-start-date').val(),
                    start_time: $('#assign-start-time').val(),
                    end_date: $('#assign-end-date').val(),
                    end_time: $('#assign-end-time').val(),
                    notes: $('#assign-notes').val()
                };

                console.log('Sending data:', formData);

                // Show loading state
                var $submitBtn = $('#lp-assign-user-course-form .lp-button-assign');
                var originalText = $submitBtn.text();
                $submitBtn.prop('disabled', true).text('Scheduling...');

                // Use WordPress AJAX URL
                var ajaxUrl = '<?php echo admin_url('admin-ajax.php'); ?>';

                $.post(ajaxUrl, formData)
                    .done(function(response) {
                        console.log('AJAX Response:', response);
                        if (response.success) {
                            alert('✅ Enrollment scheduled successfully!\n\n' + response.data.message);
                            // Reset form
                            $('#lp-assign-user-course-form')[0].reset();
                            $('#enable-scheduling').prop('checked', false);
                            $('#scheduling-options').hide();
                        } else {
                            var errorMsg = 'Failed to schedule enrollment';
                            if (response.data && response.data.message) {
                                errorMsg = response.data.message;
                                if (response.data.errors && response.data.errors.length > 0) {
                                    errorMsg += '\n\nErrors:\n' + response.data.errors.join('\n');
                                }
                            }
                            alert('❌ Error: ' + errorMsg);
                        }
                    })
                    .fail(function(xhr, status, error) {
                        console.log('AJAX Error:', xhr, status, error);
                        console.log('Response Text:', xhr.responseText);
                        alert('❌ Network error occurred: ' + error + '\n\nCheck browser console for details.');
                    })
                    .always(function() {
                        $submitBtn.prop('disabled', false).text(originalText);
                    });
            }
        });
        </script>

        <style>
        #scheduling-options {
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from { opacity: 0; max-height: 0; }
            to { opacity: 1; max-height: 300px; }
        }

        #enable-scheduling:checked + label {
            color: #0073aa;
            font-weight: 600;
        }
        </style>
        <?php
    }

    /**
     * Handle scheduled assignment via AJAX
     */
    public function handle_scheduled_assignment() {
        // Log that the handler is being called
        error_log('Learnpressium: handle_scheduled_assignment called');
        error_log('POST data: ' . print_r($_POST, true));

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'learnpressium_enrollment_nonce')) {
            error_log('Learnpressium: Nonce verification failed');
            wp_send_json_error('Security check failed');
            return;
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            error_log('Learnpressium: Permission check failed');
            wp_send_json_error('Insufficient permissions');
            return;
        }

        // Check if enrollment manager is available
        if (!$this->enrollment_manager) {
            error_log('Learnpressium: Enrollment manager not available');
            wp_send_json_error('Enrollment manager not initialized');
            return;
        }

        // Check if database table exists
        global $wpdb;
        $table_name = $wpdb->prefix . 'learnpressium_enrollment_schedules';
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;

        if (!$table_exists) {
            error_log('Learnpressium: Database table does not exist: ' . $table_name);
            wp_send_json_error('Database table not found. Please deactivate and reactivate the plugin.');
            return;
        }

        error_log('Learnpressium: Database table exists: ' . $table_name);

        $course_ids = array_map('intval', $_POST['course_ids']);
        $user_ids = array_map('intval', $_POST['user_ids']);
        $start_date = sanitize_text_field($_POST['start_date']);
        $start_time = sanitize_text_field($_POST['start_time']);
        $end_date = !empty($_POST['end_date']) ? sanitize_text_field($_POST['end_date']) : null;
        $end_time = !empty($_POST['end_time']) ? sanitize_text_field($_POST['end_time']) : null;
        $notes = sanitize_textarea_field($_POST['notes']);

        // Combine date and time
        $start_datetime = $start_date . ' ' . $start_time . ':00';
        $end_datetime = null;
        if ($end_date && $end_time) {
            $end_datetime = $end_date . ' ' . $end_time . ':00';
        }

        $results = array();
        $errors = array();

        error_log('Learnpressium: Processing schedules for ' . count($course_ids) . ' courses and ' . count($user_ids) . ' users');
        error_log('Start datetime: ' . $start_datetime);
        error_log('End datetime: ' . ($end_datetime ?: 'null'));

        foreach ($course_ids as $course_id) {
            foreach ($user_ids as $user_id) {
                error_log("Learnpressium: Scheduling user {$user_id} for course {$course_id}");

                $result = $this->enrollment_manager->schedule_enrollment(
                    $user_id,
                    $course_id,
                    $start_datetime,
                    $end_datetime,
                    $notes
                );

                if (is_wp_error($result)) {
                    $error_msg = $result->get_error_message();
                    error_log("Learnpressium: Schedule failed for user {$user_id}, course {$course_id}: {$error_msg}");
                    $errors[] = sprintf(
                        'Failed to schedule user %d for course %d: %s',
                        $user_id,
                        $course_id,
                        $error_msg
                    );
                } else {
                    error_log("Learnpressium: Schedule successful for user {$user_id}, course {$course_id}, schedule ID: {$result}");
                    $results[] = $result;
                }
            }
        }

        if (!empty($errors)) {
            wp_send_json_error(array(
                'message' => __('Some schedules could not be created', 'learnpressium'),
                'errors' => $errors,
                'success_count' => count($results)
            ));
        } else {
            wp_send_json_success(array(
                'message' => sprintf(
                    __('%d enrollment schedules created successfully', 'learnpressium'),
                    count($results)
                ),
                'schedule_ids' => $results
            ));
        }
    }

    /**
     * Register REST API routes for tools integration
     */
    public function register_rest_routes() {
        register_rest_route('learnpressium/v1', '/tools/courses', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_courses_for_tools'),
            'permission_callback' => array($this, 'check_admin_permissions')
        ));

        register_rest_route('learnpressium/v1', '/tools/users', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_users_for_tools'),
            'permission_callback' => array($this, 'check_admin_permissions')
        ));

        register_rest_route('learnpressium/v1', '/tools/schedules', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_schedules_for_tools'),
            'permission_callback' => array($this, 'check_admin_permissions')
        ));

        // Add AJAX endpoints for non-REST requests
        add_action('wp_ajax_get_courses', array($this, 'ajax_get_courses'));
        add_action('wp_ajax_get_users', array($this, 'ajax_get_users'));
    }

    /**
     * Get courses for tools dropdown
     */
    public function get_courses_for_tools($request) {
        $courses = get_posts(array(
            'post_type' => 'lp_course',
            'post_status' => 'publish',
            'numberposts' => -1,
            'orderby' => 'title',
            'order' => 'ASC'
        ));

        $course_options = array();
        foreach ($courses as $course) {
            $course_options[] = array(
                'value' => $course->ID,
                'text' => $course->post_title . ' (#' . $course->ID . ')'
            );
        }

        return rest_ensure_response($course_options);
    }

    /**
     * Get users for tools dropdown
     */
    public function get_users_for_tools($request) {
        $users = get_users(array(
            'orderby' => 'display_name',
            'order' => 'ASC'
        ));

        $user_options = array();
        foreach ($users as $user) {
            $user_options[] = array(
                'value' => $user->ID,
                'text' => $user->display_name . ' (' . $user->user_email . ') #' . $user->ID
            );
        }

        return rest_ensure_response($user_options);
    }

    /**
     * Get schedules for tools management
     */
    public function get_schedules_for_tools($request) {
        global $wpdb;
        
        $status = $request->get_param('status');
        $table_name = $wpdb->prefix . 'learnpressium_enrollment_schedules';
        
        $sql = "SELECT s.*, u.display_name as user_name, u.user_email, p.post_title as course_title 
                FROM {$table_name} s 
                LEFT JOIN {$wpdb->users} u ON s.user_id = u.ID 
                LEFT JOIN {$wpdb->posts} p ON s.course_id = p.ID";
        
        if ($status) {
            $sql .= $wpdb->prepare(" WHERE s.status = %s", $status);
        }
        
        $sql .= " ORDER BY s.scheduled_start_date DESC";
        
        $schedules = $wpdb->get_results($sql);
        
        return rest_ensure_response($schedules);
    }

    /**
     * Check admin permissions for REST API
     */
    public function check_admin_permissions() {
        return current_user_can('manage_options');
    }

    /**
     * AJAX handler for getting courses
     */
    public function ajax_get_courses() {
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        $courses = get_posts(array(
            'post_type' => 'lp_course',
            'post_status' => 'publish',
            'numberposts' => -1,
            'orderby' => 'title',
            'order' => 'ASC'
        ));

        $course_options = array();
        foreach ($courses as $course) {
            $course_options[] = array(
                'value' => $course->ID,
                'text' => $course->post_title . ' (#' . $course->ID . ')'
            );
        }

        wp_send_json_success($course_options);
    }

    /**
     * AJAX handler for getting users
     */
    public function ajax_get_users() {
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        $users = get_users(array(
            'orderby' => 'display_name',
            'order' => 'ASC'
        ));

        $user_options = array();
        foreach ($users as $user) {
            $user_options[] = array(
                'value' => $user->ID,
                'text' => $user->display_name . ' (' . $user->user_email . ') #' . $user->ID
            );
        }

        wp_send_json_success($user_options);
    }
}
