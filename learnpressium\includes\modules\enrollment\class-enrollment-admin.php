<?php

/**
 * The Enrollment Admin class
 *
 * Handles admin interface for enrollment scheduling
 *
 * @package    Learnpressium
 * @subpackage Learnpressium/includes/modules/enrollment
 */

class Enrollment_Admin {

    /**
     * The enrollment manager instance
     */
    private $enrollment_manager;

    /**
     * Constructor
     */
    public function __construct($enrollment_manager) {
        $this->enrollment_manager = $enrollment_manager;
    }

    /**
     * Initialize admin interface
     */
    public function init() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('add_meta_boxes', array($this, 'add_course_meta_boxes'));
        add_action('save_post', array($this, 'save_course_meta_box'));
        add_action('show_user_profile', array($this, 'add_user_profile_section'));
        add_action('edit_user_profile', array($this, 'add_user_profile_section'));
    }

    /**
     * Add admin menu for enrollment management
     */
    public function add_admin_menu() {
        add_submenu_page(
            'learn_press',
            __('Enrollment Schedules', 'learnpressium'),
            __('Enrollment Schedules', 'learnpressium'),
            'manage_options',
            'learnpressium-enrollment-schedules',
            array($this, 'display_schedules_page')
        );
    }

    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        // Only load on our admin pages
        if (strpos($hook, 'learnpressium') === false && 
            strpos($hook, 'learn-press') === false && 
            $hook !== 'post.php' && 
            $hook !== 'post-new.php' &&
            $hook !== 'profile.php' &&
            $hook !== 'user-edit.php') {
            return;
        }

        wp_enqueue_script('jquery-ui-datepicker');
        wp_enqueue_style('jquery-ui-datepicker', 'https://code.jquery.com/ui/1.12.1/themes/ui-lightness/jquery-ui.css');
        
        wp_enqueue_script(
            'learnpressium-enrollment-admin',
            LEARNPRESSIUM_PLUGIN_URL . 'admin/js/enrollment-admin.js',
            array('jquery', 'jquery-ui-datepicker'),
            LEARNPRESSIUM_VERSION,
            true
        );

        wp_enqueue_style(
            'learnpressium-enrollment-admin',
            LEARNPRESSIUM_PLUGIN_URL . 'admin/css/enrollment-admin.css',
            array(),
            LEARNPRESSIUM_VERSION
        );

        wp_localize_script('learnpressium-enrollment-admin', 'learnpressium_enrollment', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('learnpressium_enrollment_nonce'),
            'strings' => array(
                'confirm_delete' => __('Are you sure you want to delete this schedule?', 'learnpressium'),
                'schedule_saved' => __('Schedule saved successfully', 'learnpressium'),
                'schedule_deleted' => __('Schedule deleted successfully', 'learnpressium'),
                'error_occurred' => __('An error occurred', 'learnpressium'),
                'loading' => __('Loading...', 'learnpressium')
            )
        ));
    }

    /**
     * Display enrollment schedules page
     */
    public function display_schedules_page() {
        ?>
        <div class="wrap">
            <h1><?php _e('Enrollment Schedules', 'learnpressium'); ?></h1>
            
            <div id="learnpressium-enrollment-schedules">
                <div class="tablenav top">
                    <div class="alignleft actions">
                        <select id="filter-by-status">
                            <option value=""><?php _e('All Statuses', 'learnpressium'); ?></option>
                            <option value="scheduled"><?php _e('Scheduled', 'learnpressium'); ?></option>
                            <option value="active"><?php _e('Active', 'learnpressium'); ?></option>
                            <option value="expired"><?php _e('Expired', 'learnpressium'); ?></option>
                        </select>
                        <button type="button" class="button" id="filter-schedules"><?php _e('Filter', 'learnpressium'); ?></button>
                    </div>
                    <div class="alignright actions">
                        <button type="button" class="button button-primary" id="add-new-schedule">
                            <?php _e('Add New Schedule', 'learnpressium'); ?>
                        </button>
                    </div>
                </div>

                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e('User', 'learnpressium'); ?></th>
                            <th><?php _e('Course', 'learnpressium'); ?></th>
                            <th><?php _e('Start Date', 'learnpressium'); ?></th>
                            <th><?php _e('End Date', 'learnpressium'); ?></th>
                            <th><?php _e('Status', 'learnpressium'); ?></th>
                            <th><?php _e('Actions', 'learnpressium'); ?></th>
                        </tr>
                    </thead>
                    <tbody id="schedules-table-body">
                        <tr>
                            <td colspan="6" class="text-center">
                                <span class="spinner is-active"></span>
                                <?php _e('Loading schedules...', 'learnpressium'); ?>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Add/Edit Schedule Modal -->
            <div id="schedule-modal" class="learnpressium-modal" style="display: none;">
                <div class="learnpressium-modal-content">
                    <div class="learnpressium-modal-header">
                        <h2 id="modal-title"><?php _e('Add New Schedule', 'learnpressium'); ?></h2>
                        <span class="learnpressium-modal-close">&times;</span>
                    </div>
                    <div class="learnpressium-modal-body">
                        <form id="schedule-form">
                            <input type="hidden" id="schedule-id" name="schedule_id" value="">
                            
                            <table class="form-table">
                                <tr>
                                    <th scope="row">
                                        <label for="user-select"><?php _e('User', 'learnpressium'); ?></label>
                                    </th>
                                    <td>
                                        <select id="user-select" name="user_id" required>
                                            <option value=""><?php _e('Select User', 'learnpressium'); ?></option>
                                        </select>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row">
                                        <label for="course-select"><?php _e('Course', 'learnpressium'); ?></label>
                                    </th>
                                    <td>
                                        <select id="course-select" name="course_id" required>
                                            <option value=""><?php _e('Select Course', 'learnpressium'); ?></option>
                                        </select>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row">
                                        <label for="start-date"><?php _e('Start Date', 'learnpressium'); ?></label>
                                    </th>
                                    <td>
                                        <input type="text" id="start-date" name="start_date" class="datepicker" required>
                                        <input type="time" id="start-time" name="start_time" value="09:00">
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row">
                                        <label for="end-date"><?php _e('End Date (Optional)', 'learnpressium'); ?></label>
                                    </th>
                                    <td>
                                        <input type="text" id="end-date" name="end_date" class="datepicker">
                                        <input type="time" id="end-time" name="end_time" value="23:59">
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row">
                                        <label for="schedule-notes"><?php _e('Notes', 'learnpressium'); ?></label>
                                    </th>
                                    <td>
                                        <textarea id="schedule-notes" name="notes" rows="3" cols="50"></textarea>
                                    </td>
                                </tr>
                            </table>
                        </form>
                    </div>
                    <div class="learnpressium-modal-footer">
                        <button type="button" class="button button-primary" id="save-schedule">
                            <?php _e('Save Schedule', 'learnpressium'); ?>
                        </button>
                        <button type="button" class="button" id="cancel-schedule">
                            <?php _e('Cancel', 'learnpressium'); ?>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <style>
        .learnpressium-modal {
            position: fixed;
            z-index: 100000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .learnpressium-modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            padding: 0;
            border: 1px solid #888;
            width: 80%;
            max-width: 600px;
            border-radius: 4px;
        }

        .learnpressium-modal-header {
            padding: 15px 20px;
            background-color: #f1f1f1;
            border-bottom: 1px solid #ddd;
            position: relative;
        }

        .learnpressium-modal-header h2 {
            margin: 0;
        }

        .learnpressium-modal-close {
            position: absolute;
            right: 15px;
            top: 15px;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .learnpressium-modal-body {
            padding: 20px;
        }

        .learnpressium-modal-footer {
            padding: 15px 20px;
            background-color: #f1f1f1;
            border-top: 1px solid #ddd;
            text-align: right;
        }

        .learnpressium-modal-footer .button {
            margin-left: 10px;
        }

        .datepicker {
            width: 150px;
        }

        input[type="time"] {
            width: 100px;
            margin-left: 10px;
        }

        .text-center {
            text-align: center;
        }

        .status-scheduled {
            color: #0073aa;
        }

        .status-active {
            color: #46b450;
        }

        .status-expired {
            color: #dc3232;
        }
        </style>
        <?php
    }

    /**
     * Add meta box to course edit page
     */
    public function add_course_meta_boxes() {
        add_meta_box(
            'learnpressium-enrollment-schedules',
            __('Enrollment Schedules', 'learnpressium'),
            array($this, 'display_course_schedules_meta_box'),
            'lp_course',
            'normal',
            'default'
        );
    }

    /**
     * Display course schedules meta box
     */
    public function display_course_schedules_meta_box($post) {
        $course_id = $post->ID;
        ?>
        <div id="learnpressium-course-schedules">
            <p><?php _e('Manage enrollment schedules for this course:', 'learnpressium'); ?></p>
            
            <button type="button" class="button button-secondary" id="add-course-schedule" data-course-id="<?php echo $course_id; ?>">
                <?php _e('Add New Schedule', 'learnpressium'); ?>
            </button>
            
            <div id="course-schedules-list">
                <table class="wp-list-table widefat fixed striped" style="margin-top: 15px;">
                    <thead>
                        <tr>
                            <th><?php _e('User', 'learnpressium'); ?></th>
                            <th><?php _e('Start Date', 'learnpressium'); ?></th>
                            <th><?php _e('End Date', 'learnpressium'); ?></th>
                            <th><?php _e('Status', 'learnpressium'); ?></th>
                            <th><?php _e('Actions', 'learnpressium'); ?></th>
                        </tr>
                    </thead>
                    <tbody id="course-schedules-tbody">
                        <tr>
                            <td colspan="5" class="text-center">
                                <span class="spinner is-active"></span>
                                <?php _e('Loading schedules...', 'learnpressium'); ?>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <?php
    }

    /**
     * Save course meta box data
     */
    public function save_course_meta_box($post_id) {
        // This is handled via AJAX, so no action needed here
        // But we keep this method for potential future use
    }

    /**
     * Add enrollment schedules section to user profile
     */
    public function add_user_profile_section($user) {
        if (!current_user_can('manage_options')) {
            return;
        }
        ?>
        <h3><?php _e('Enrollment Schedules', 'learnpressium'); ?></h3>
        <div id="learnpressium-user-schedules">
            <p><?php _e('Manage enrollment schedules for this user:', 'learnpressium'); ?></p>
            
            <button type="button" class="button button-secondary" id="add-user-schedule" data-user-id="<?php echo $user->ID; ?>">
                <?php _e('Add New Schedule', 'learnpressium'); ?>
            </button>
            
            <div id="user-schedules-list">
                <table class="wp-list-table widefat fixed striped" style="margin-top: 15px;">
                    <thead>
                        <tr>
                            <th><?php _e('Course', 'learnpressium'); ?></th>
                            <th><?php _e('Start Date', 'learnpressium'); ?></th>
                            <th><?php _e('End Date', 'learnpressium'); ?></th>
                            <th><?php _e('Status', 'learnpressium'); ?></th>
                            <th><?php _e('Actions', 'learnpressium'); ?></th>
                        </tr>
                    </thead>
                    <tbody id="user-schedules-tbody">
                        <tr>
                            <td colspan="5" class="text-center">
                                <span class="spinner is-active"></span>
                                <?php _e('Loading schedules...', 'learnpressium'); ?>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <?php
    }
}
