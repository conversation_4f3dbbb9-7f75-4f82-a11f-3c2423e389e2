<?php
/**
 * Simple test file to verify Enrollment Module functionality
 * 
 * This file can be run from WordPress admin or via WP-CLI to test basic functionality
 * 
 * Usage: 
 * - Place this file in the plugin root
 * - Access via: yoursite.com/wp-content/plugins/learnpressium/test-enrollment-module.php
 * - Or run via WP-CLI: wp eval-file test-enrollment-module.php
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress if not already loaded
    $wp_load_path = dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php';
    if (file_exists($wp_load_path)) {
        require_once $wp_load_path;
    } else {
        die('WordPress not found. Please run this from within WordPress context.');
    }
}

// Check if Learnpressium is active
if (!class_exists('Learnpressium')) {
    die('Learnpressium plugin is not active.');
}

// Check if LearnPress is active
if (!class_exists('LearnPress')) {
    die('LearnPress plugin is not active.');
}

echo "<h1>Learnpressium Enrollment Module Test</h1>\n";

// Test 1: Check if constants are defined
echo "<h2>1. Constants Test</h2>\n";
$constants = ['LEARNPRESSIUM_VERSION', 'LEARNPRESSIUM_PLUGIN_DIR', 'LEARNPRESSIUM_PLUGIN_URL', 'LEARNPRESSIUM_PLUGIN_FILE'];
foreach ($constants as $constant) {
    if (defined($constant)) {
        echo "✅ {$constant}: " . constant($constant) . "<br>\n";
    } else {
        echo "❌ {$constant}: Not defined<br>\n";
    }
}

// Test 2: Check if enrollment module classes exist
echo "<h2>2. Class Existence Test</h2>\n";
$classes = [
    'Enrollment_Module',
    'Enrollment_Manager', 
    'Enrollment_Database',
    'Enrollment_Access_Controller',
    'Enrollment_Admin',
    'Enrollment_Tools_Integration',
    'Enrollment_Frontend'
];

foreach ($classes as $class) {
    if (class_exists($class)) {
        echo "✅ {$class}: Class exists<br>\n";
    } else {
        echo "❌ {$class}: Class not found<br>\n";
    }
}

// Test 3: Check database table
echo "<h2>3. Database Table Test</h2>\n";
global $wpdb;
$table_name = $wpdb->prefix . 'learnpressium_enrollment_schedules';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") == $table_name;

if ($table_exists) {
    echo "✅ Database table '{$table_name}' exists<br>\n";
    
    // Check table structure
    $columns = $wpdb->get_results("DESCRIBE {$table_name}");
    echo "Table columns:<br>\n";
    foreach ($columns as $column) {
        echo "&nbsp;&nbsp;- {$column->Field} ({$column->Type})<br>\n";
    }
} else {
    echo "❌ Database table '{$table_name}' does not exist<br>\n";
    echo "Try deactivating and reactivating the plugin to create the table.<br>\n";
}

// Test 4: Check LearnPress integration
echo "<h2>4. LearnPress Integration Test</h2>\n";
$lp_functions = [
    'learn_press_update_user_item_meta',
    'learn_press_get_user_item_meta'
];

foreach ($lp_functions as $function) {
    if (function_exists($function)) {
        echo "✅ {$function}: Function available<br>\n";
    } else {
        echo "❌ {$function}: Function not available<br>\n";
    }
}

// Test 5: Check WordPress hooks
echo "<h2>5. WordPress Hooks Test</h2>\n";
$hooks_to_check = [
    'learnpress/course/can-view-content',
    'learn-press/user/can-enroll/course',
    'learnpress/user/course-enrolled'
];

foreach ($hooks_to_check as $hook) {
    $priority = has_filter($hook);
    if ($priority !== false) {
        echo "✅ {$hook}: Hook registered (priority: {$priority})<br>\n";
    } else {
        echo "⚠️ {$hook}: Hook not registered (may be normal if module not fully initialized)<br>\n";
    }
}

// Test 6: Check cron job
echo "<h2>6. Cron Job Test</h2>\n";
$cron_hook = 'learnpressium_process_scheduled_enrollments';
$next_scheduled = wp_next_scheduled($cron_hook);

if ($next_scheduled) {
    echo "✅ Cron job '{$cron_hook}' is scheduled for: " . date('Y-m-d H:i:s', $next_scheduled) . "<br>\n";
} else {
    echo "⚠️ Cron job '{$cron_hook}' is not scheduled<br>\n";
}

// Test 7: Check admin pages
echo "<h2>7. Admin Pages Test</h2>\n";
if (is_admin()) {
    global $submenu;
    $lp_submenu = isset($submenu['learn_press']) ? $submenu['learn_press'] : [];
    
    $enrollment_page_found = false;
    foreach ($lp_submenu as $item) {
        if (isset($item[2]) && $item[2] === 'learnpressium-enrollment-schedules') {
            $enrollment_page_found = true;
            break;
        }
    }
    
    if ($enrollment_page_found) {
        echo "✅ Enrollment Schedules admin page is registered<br>\n";
    } else {
        echo "⚠️ Enrollment Schedules admin page not found (may be normal if not in admin context)<br>\n";
    }
} else {
    echo "ℹ️ Not in admin context, skipping admin page test<br>\n";
}

// Test 8: Simple database operation test
echo "<h2>8. Database Operation Test</h2>\n";
if ($table_exists && class_exists('Enrollment_Database')) {
    try {
        $db = new Enrollment_Database();
        
        // Test getting schedules (should return empty array if no data)
        $schedules = $wpdb->get_results("SELECT COUNT(*) as count FROM {$table_name}");
        $count = $schedules[0]->count ?? 0;
        
        echo "✅ Database operations working. Current schedule count: {$count}<br>\n";
    } catch (Exception $e) {
        echo "❌ Database operation failed: " . $e->getMessage() . "<br>\n";
    }
} else {
    echo "⚠️ Skipping database operation test (table or class not available)<br>\n";
}

// Test 9: Check file permissions and paths
echo "<h2>9. File System Test</h2>\n";
$files_to_check = [
    LEARNPRESSIUM_PLUGIN_DIR . 'admin/css/enrollment-admin.css',
    LEARNPRESSIUM_PLUGIN_DIR . 'admin/js/enrollment-admin.js',
    LEARNPRESSIUM_PLUGIN_DIR . 'public/css/enrollment-frontend.css',
    LEARNPRESSIUM_PLUGIN_DIR . 'public/js/enrollment-frontend.js'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $size = filesize($file);
        echo "✅ " . basename($file) . ": Exists ({$size} bytes)<br>\n";
    } else {
        echo "❌ " . basename($file) . ": Not found<br>\n";
    }
}

// Summary
echo "<h2>Test Summary</h2>\n";
echo "<p>If you see mostly green checkmarks (✅), the Enrollment Module is properly installed and configured.</p>\n";
echo "<p>Yellow warnings (⚠️) may be normal depending on context.</p>\n";
echo "<p>Red X marks (❌) indicate issues that need to be addressed.</p>\n";

echo "<hr>\n";
echo "<p><strong>Next Steps:</strong></p>\n";
echo "<ol>\n";
echo "<li>If database table is missing, try deactivating and reactivating the plugin</li>\n";
echo "<li>Go to LearnPress → Tools → Scheduled Enrollment to test the interface</li>\n";
echo "<li>Create a test schedule to verify functionality</li>\n";
echo "<li>Check LearnPress → Enrollment Schedules for the management interface</li>\n";
echo "</ol>\n";

echo "<p><em>Test completed at: " . date('Y-m-d H:i:s') . "</em></p>\n";
?>
